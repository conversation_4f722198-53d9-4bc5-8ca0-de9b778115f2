import type React from "react"
import type { Metadata } from "next"
import "./globals.css"
import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: {
    default: "Musasa - Digital Experience Crafters",
    template: "%s | Musasa"
  },
  description: "Transform your ideas into powerful web applications. We build fast, scalable, and beautiful websites that drive results for your business.",
  keywords: ["web development", "UI/UX design", "Mobile Development", "web design", "digital agency", "AI Integration", "AI Automation", "AI Agency", "Zimbabwe", "Africa"],
  authors: [{ name: "Musasa Team" }],
  creator: "<PERSON><PERSON>",
  publisher: "<PERSON>sa",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://musasa.dev',
    title: 'Musasa - Digital Experience Crafters',
    description: 'Transform your ideas into powerful web applications. We build fast, scalable, and beautiful websites, high ranking websites',
    siteName: 'Musasa',
    images: [{
      url: '/og-image.jpg',
      width: 1200,
      height: 630,
      alt: 'Musasa - Digital Experience Crafters',
    }],
  },
  // twitter: {
  //   card: 'summary_large_image',
  //   title: 'Musasa - Digital Experience Crafters',
  //   description: 'Transform your ideas into powerful web applications.',
  //   images: ['/og-image.jpg'],
  // },
  // verification: {
  //   google: 'your-google-verification-code',
  // },
  alternates: {
    canonical: 'https://musasa.dev',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
