"use client"

import { motion } from "framer-motion"
import { ThemeToggle } from "@/components/theme-toggle"
import { FullPageAnimation } from "@/components/hero-animation"
import { MorphingBlob, InteractiveParticles } from "@/components/unique-effects"
import { PricingSection } from "@/components/pricing-section"
import { ContactForm } from "@/components/contact-form"
import { FadeInUp, FloatingAnimation } from "@/components/animated-elements"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Code2,
  Palette,
  Smartphone,
  Zap,
  Users,
  Award,
  ArrowRight,
  CheckCircle,
  Star,
  Globe,
  Rocket,
  Shield,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import { Menu, X } from "lucide-react"

export default function LandingPage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "<PERSON><PERSON>",
    "description": "Digital experience crafters specializing in web development and UI/UX design",
    "url": "https://musasa.dev",
    "logo": "https://musasa.dev/musasa_darker_green.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "url": "https://cal.com/shaunnn/discovery-session"
    },
    "sameAs": [
      "https://twitter.com/musasa",
      "https://linkedin.com/company/musasa"
    ],
    "offers": {
      "@type": "Service",
      "serviceType": "Web Development",
      "description": "Custom websites and web applications"
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="flex flex-col min-h-screen bg-white dark:bg-gray-950 transition-colors relative overflow-hidden">
        {/* Multiple Background Layers */}
        <FullPageAnimation />
        <MorphingBlob />
        <InteractiveParticles />

        {/* All content with relative positioning */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header - Mobile Responsive */}
          <header className="fixed top-4 left-4 right-4 md:left-36 md:right-36 z-50 w-auto bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border border-gray-200/30 dark:border-gray-700/30 shadow-lg rounded-2xl">
            <div className="mx-auto max-w-[99.5%]">
              <div className="flex h-16 md:h-20 items-center justify-between px-4 md:px-12">
                {/* Logo */}
                <motion.div
                  className="flex items-center space-x-2 md:space-x-3"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <motion.div
                    className="flex h-8 w-8 md:h-10 md:w-10 items-center justify-center rounded-lg bg-transparent"
                    animate={{
                      boxShadow: ["0 0 20px rgba(0,0,0,0.3)", "0 0 30px rgba(76,170,125,0.5)", "0 0 20px rgba(0,0,0,0.3)"],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: "easeInOut",
                    }}
                  >
                    <Image
                      src="/musasa_darker_green.png"
                      alt="Musasa Logo"
                      width={32}
                      height={32}
                      className="h-8 w-8 md:h-10 md:w-10 object-contain dark:hidden"
                      priority
                      sizes="(max-width: 768px) 32px, 40px"
                    />
                    <Image
                      src="/musasa_white.png"
                      alt="Musasa Logo"
                      width={32}
                      height={32}
                      className="h-8 w-8 md:h-10 md:w-10 object-contain hidden dark:block"
                      priority
                      sizes="(max-width: 768px) 32px, 40px"
                    />
                  </motion.div>
                  <span className="text-lg md:text-2xl font-bold text-black dark:text-white">MUSASA</span>
                </motion.div>

                {/* Desktop Navigation */}
                <nav className="hidden lg:flex items-center space-x-8">
                  {["Services", "Work", "About", "Pricing", "Contact"].map((item, index) => (
                    <motion.div key={item} whileHover={{ y: -2 }} transition={{ type: "spring", stiffness: 300 }}>
                      <Link
                        href={`#${item.toLowerCase()}`}
                        className="text-base font-medium hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100 transition-colors relative"
                      >
                        {item}
                        <motion.div
                          className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-gray-600 to-gray-800 origin-left"
                          initial={{ scaleX: 0 }}
                          whileHover={{ scaleX: 1 }}
                          transition={{ duration: 0.3 }}
                        />
                      </Link>
                    </motion.div>
                  ))}
                </nav>

                {/* Tablet Navigation */}
                <nav className="hidden md:flex lg:hidden items-center space-x-6">
                  {["Services", "Work", "About", "Pricing", "Contact"].map((item) => (
                    <motion.div key={item} whileHover={{ y: -2 }} transition={{ type: "spring", stiffness: 300 }}>
                      <Link
                        href={`#${item.toLowerCase()}`}
                        className="text-sm font-medium hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100 transition-colors relative px-2 py-1"
                      >
                        {item}
                        <motion.div
                          className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-gray-600 to-gray-800 origin-left"
                          initial={{ scaleX: 0 }}
                          whileHover={{ scaleX: 1 }}
                          transition={{ duration: 0.3 }}
                        />
                      </Link>
                    </motion.div>
                  ))}
                </nav>

                {/* Right side - Theme toggle and CTA */}
                <div className="flex items-center space-x-2 md:space-x-4">
                  <ThemeToggle />
                  
                  {/* Desktop CTA Button */}
                  <motion.div
                    className="hidden md:block"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    animate={{
                      boxShadow: [
                        "0 4px 20px rgba(0,0,0,0.1)",
                        "0 8px 30px rgba(76,170,125,0.3)",
                        "0 4px 20px rgba(0,0,0,0.1)",
                      ],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: "easeInOut",
                    }}
                  >
                    <Button 
                      className="bg-black hover:bg-gray-800 dark:bg-white dark:text-black dark:hover:bg-gray-200 relative overflow-hidden text-base px-6 py-3"
                      onClick={() => window.open('https://cal.com/shaunnn/discovery-session', '_blank')}
                    >
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-gray-700 to-gray-800"
                        initial={{ x: "-100%" }}
                        whileHover={{ x: "0%" }}
                        transition={{ duration: 0.3 }}
                      />
                      <span className="relative z-10">Get Started</span>
                      <ArrowRight className="ml-2 h-4 w-4 relative z-10" />
                    </Button>
                  </motion.div>

                  {/* Mobile Menu Button */}
                  <button
                    className="lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                    onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                    aria-label="Toggle mobile menu"
                  >
                    {mobileMenuOpen ? (
                      <X className="h-5 w-5 text-gray-600 dark:text-gray-300" />
                    ) : (
                      <Menu className="h-5 w-5 text-gray-600 dark:text-gray-300" />
                    )}
                  </button>
                </div>
              </div>

              {/* Mobile Menu */}
              {mobileMenuOpen && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2 }}
                  className="lg:hidden border-t border-gray-200/30 dark:border-gray-700/30 px-4 py-4"
                >
                  <nav className="flex flex-col space-y-4">
                    {["Services", "Work", "About", "Pricing", "Contact"].map((item) => (
                      <Link
                        key={item}
                        href={`#${item.toLowerCase()}`}
                        className="text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors py-2"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        {item}
                      </Link>
                    ))}
                    <Button 
                      className="bg-black hover:bg-gray-800 dark:bg-white dark:text-black dark:hover:bg-gray-200 w-full mt-4"
                      onClick={() => {
                        window.open('https://cal.com/shaunnn/discovery-session', '_blank')
                        setMobileMenuOpen(false)
                      }}
                    >
                      Get Started
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </nav>
                </motion.div>
              )}
            </div>
          </header>

          <main className="flex-1 pt-28">
            {/* Hero Section */}
            <section className="relative py-20 md:py-32 lg:py-40 overflow-hidden">
              <div className="px-4 md:px-6 relative z-10">
                <div className="mx-auto max-w-4xl text-center">
                  <motion.div
                    animate={{
                      scale: [1, 1.05, 1],
                      rotate: [0, 1, -1, 0],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: "easeInOut",
                    }}
                  >
                    <Badge
                      variant="outline"
                      className="mb-6 dark:border-gray-700 dark:text-gray-300 backdrop-blur-sm bg-white/20 dark:bg-gray-800/20"
                    >
                      <Rocket className="mr-2 h-3 w-3" />
                      Trusted by 20+ companies
                    </Badge>
                  </motion.div>

                  <motion.h1
                    className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl mb-6 dark:text-white"
                    animate={{
                      textShadow: [
                        "0 0 40px rgba(76,170,125,0.3)",
                        "0 0 60px rgba(76,170,125,0.4)",
                        "0 0 40px rgba(76,170,125,0.3)",
                      ],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: "easeInOut",
                    }}
                  >
                    We craft digital
                    <motion.span
                      className="block text-gray-600 dark:text-gray-400"
                      animate={{
                        backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                      }}
                      transition={{
                        duration: 5,
                        repeat: Number.POSITIVE_INFINITY,
                        ease: "easeInOut",
                      }}
                      style={{
                        background: "linear-gradient(45deg, #4CAA7D, #4CAA7D, #4CAA7D, #4CAA7D)",
                        backgroundSize: "200% 200%",
                        WebkitBackgroundClip: "text",
                        WebkitTextFillColor: "transparent",
                        backgroundClip: "text",
                      }}
                    >
                      experiences
                    </motion.span>
                  </motion.h1>

                  <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300 mb-8">
                    Transform your ideas into powerful web applications. We build fast, scalable, and beautiful websites
                    that drive results for your business.
                  </p>

                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <motion.div whileHover={{ scale: 1.05, y: -5 }} whileTap={{ scale: 0.95 }} className="relative">
                      <Button
                        size="lg"
                        className="bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-900 hover:to-black text-white border-0 relative overflow-hidden"
                      >
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-[#4CAA7D] to-[#4CAA7D]"
                          initial={{ x: "-100%" }}
                          whileHover={{ x: "0%" }}
                          transition={{ duration: 0.3 }}
                        />
                        <span className="relative z-10">Start Your Project</span>
                        <ArrowRight className="ml-2 h-4 w-4 relative z-10" />
                      </Button>
                    </motion.div>
                  </div>
                </div>
              </div>

              {/* Enhanced floating elements */}
              <FloatingAnimation>
                <motion.div
                  className="absolute top-20 left-10 opacity-10 dark:opacity-5"
                  animate={{
                    rotate: [0, 360],
                    scale: [1, 1.2, 1],
                  }}
                  transition={{
                    duration: 8,
                    repeat: Number.POSITIVE_INFINITY,
                    ease: "easeInOut",
                  }}
                >
                  <div className="h-20 w-20 rounded-full bg-gradient-to-r from-gray-300 to-gray-400 blur-sm"></div>
                </motion.div>
              </FloatingAnimation>
              <FloatingAnimation>
                <motion.div
                  className="absolute bottom-20 right-10 opacity-10 dark:opacity-5"
                  animate={{
                    rotate: [360, 0],
                    scale: [1.2, 1, 1.2],
                  }}
                  transition={{
                    duration: 6,
                    repeat: Number.POSITIVE_INFINITY,
                    ease: "easeInOut",
                  }}
                >
                  <div className="h-16 w-16 rounded-lg bg-gradient-to-r from-[#4CAA7D] to-[#4CAA7D] blur-sm"></div>
                </motion.div>
              </FloatingAnimation>
            </section>

            {/* Services Section */}
            <section
              id="services"
              className="py-20 bg-gradient-to-br from-gray-50/80 via-white/50 to-gray-100/80 dark:from-gray-900/80 dark:via-gray-950/50 dark:to-gray-900/80 transition-colors backdrop-blur-sm"
            >
              <div className="px-4 md:px-6">
                <div className="mx-auto max-w-2xl text-center mb-16">
                  <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4 dark:text-white">
                    What we do best
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300">
                    We specialize in creating modern web solutions that help businesses thrive in the digital world.
                  </p>
                </div>

                <div className="grid gap-8 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 max-w-4xl mx-auto">
                  {[
                    {
                      icon: Code2,
                      title: "Web Development",
                      description:
                        "Custom websites and web applications built with modern technologies like React, Next.js, and Node.js.",
                      features: ["Responsive Design", "Performance Optimized"],
                      gradient: "from-[#4CAA7D] to-[#4CAA7D]",
                    },
                    {
                      icon: Palette,
                      title: "UI/UX Design",
                      description:
                        "Beautiful, intuitive designs that provide exceptional user experiences and drive conversions.",
                      features: ["User-Centered Design", "Conversion Focused"],
                      gradient: "from-gray-600 to-gray-700",
                    },
                    {
                      icon: Smartphone,
                      title: "Mobile Apps",
                      description:
                        "Native and cross-platform mobile applications that deliver seamless experiences across devices.",
                      features: ["iOS & Android", "React Native"],
                      gradient: "from-green-500 to-green-700",
                    },
                  ].map((service) => (
                    <div key={service.title} className="group">
                      <Card className="border-0 shadow-lg hover:shadow-2xl transition-all duration-500 backdrop-blur-xl bg-white/90 dark:bg-gray-900/90 border-gray-200/30 dark:border-gray-700/30 overflow-hidden relative">
                        <div className={`absolute inset-0 bg-gradient-to-r ${service.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`} />
                        <CardContent className="p-8 relative z-10">
                          <div className="mb-4">
                            <div className={`flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-r from-[#4CAA7D] to-[#4CAA7D] shadow-lg`}>
                              <service.icon className="h-6 w-6 text-white" />
                            </div>
                          </div>
                          <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{service.title}</h3>
                          <p className="text-gray-600 dark:text-gray-300 mb-4">{service.description}</p>
                          <ul className="space-y-2">
                            {service.features.map((feature) => (
                              <li key={feature} className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                <CheckCircle className="mr-2 h-4 w-4 text-[#4CAA7D]" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Stats Section */}
            <section className="py-20 dark:bg-gray-950/80 transition-colors backdrop-blur-sm relative overflow-hidden">
              <motion.div
                className="absolute inset-0 opacity-10"
                animate={{
                  background: [
                    "linear-gradient(45deg, transparent, rgba(147,51,234,0.1), transparent)",
                    "linear-gradient(135deg, transparent, rgba(236,72,153,0.1), transparent)",
                    "linear-gradient(45deg, transparent, rgba(147,51,234,0.1), transparent)",
                  ],
                }}
                transition={{
                  duration: 8,
                  repeat: Number.POSITIVE_INFINITY,
                  ease: "easeInOut",
                }}
              />
              <div className="container px-4 md:px-6 relative z-10">
                <div className="grid gap-8 md:grid-cols-4 text-center">
                  {[
                    { number: "100+", label: "Projects Completed" },
                    { number: "20+", label: "Happy Clients" },
                    { number: "5+", label: "Years Experience" },
                    { number: "24/7", label: "Support" },
                  ].map((stat, index) => (
                    <FadeInUp key={stat.label} delay={index * 0.1}>
                      <motion.div
                        whileHover={{ scale: 1.1, y: -5 }}
                        transition={{ type: "spring", stiffness: 300 }}
                        className="group"
                      >
                        <motion.div
                          className="text-4xl font-bold mb-2 dark:text-white"
                          animate={{
                            textShadow: [
                              "0 0 20px rgba(107,114,128,0.5)",
                              "0 0 30px rgba(156,163,175,0.6)",
                              "0 0 20px rgba(107,114,128,0.5)",
                            ],
                          }}
                          transition={{
                            duration: 3,
                            repeat: Number.POSITIVE_INFINITY,
                            ease: "easeInOut",
                            delay: index * 0.5,
                          }}
                        >
                          {stat.number}
                        </motion.div>
                        <div className="text-gray-600 dark:text-gray-300 group-hover:text-[#4CAA7D] dark:group-hover:text-[#4CAA7D] transition-colors">
                          {stat.label}
                        </div>
                      </motion.div>
                    </FadeInUp>
                  ))}
                </div>
              </div>
            </section>

            {/* Why Choose Us */}
            <section
              id="about"
              className="py-20 bg-gradient-to-br from-gray-50/80 via-white/50 to-gray-100/80 dark:from-gray-900/80 dark:via-gray-950/50 dark:to-gray-900/80 transition-colors backdrop-blur-sm"
            >
              <div className="px-4 md:px-6">
                <div className="grid gap-12 lg:grid-cols-2 items-center">
                  <div>
                    <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6 dark:text-white">
                      Why choose Musasa?
                    </h2>
                    <p className="text-gray-600 dark:text-gray-300 mb-8">
                      We combine technical expertise with creative vision to deliver web solutions that not only look
                      great but perform exceptionally.
                    </p>

                    <div className="space-y-6">
                      {[
                        { icon: Zap, title: "Lightning Fast", desc: "Optimized for speed and performance" },
                        { icon: Shield, title: "Secure & Reliable", desc: "Built with security best practices" },
                        { icon: Users, title: "Expert Team", desc: "Experienced developers and designers" },
                      ].map((item) => (
                        <div key={item.title} className="flex items-start space-x-4 group">
                          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-[#4CAA7D] to-[#4CAA7D] shadow-lg">
                            <item.icon className="h-4 w-4 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold mb-1 dark:text-white group-hover:text-[#4CAA7D] dark:group-hover:text-[#4CAA7D] transition-colors">
                              {item.title}
                            </h3>
                            <p className="text-gray-600 dark:text-gray-300 text-sm">{item.desc}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#4CAA7D] to-[#4CAA7D] rounded-lg blur-2xl opacity-20" />
                    <Image
                      src="/medium-shot-people-travel-agency-office.jpg"
                      alt="Team working"
                      width={500}
                      height={400}
                      className="rounded-lg shadow-2xl relative z-10"
                    />
                  </div>
                </div>
              </div>
            </section>

            {/* Testimonials */}
            <section className="py-20 dark:bg-gray-950/80 transition-colors backdrop-blur-sm">
              <div className="px-4 md:px-6">
                <div className="mx-auto max-w-2xl text-center mb-16">
                  <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4 dark:text-white">
                    What our clients say
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300">
                    Don't just take our word for it. Here's what our clients have to say about working with us.
                  </p>
                </div>

                <div className="grid gap-8 md:grid-cols-3">
                  {[
                    {
                      name: "Neolin Nemaungwe",
                      role: "CEO, Neovertex Investments",
                      content:
                        "Musasa transformed our online presence completely. The new website brings us tonnes of leads every month",
                      image: "/neovertex.jpg"
                    },
                    {
                      name: "Lincoln Muchenje",
                      role: "I.T Executive, Transerv Zimbabwe",
                      content:
                        "Professional, reliable, and incredibly talented. They delivered exactly what we needed, on time and within budget.",
                      image: "/transerv.jpg"
                    },
                    {
                      name: "Edmond Sithole",
                      role: "Head of New Ventures, ZSE",
                      content:
                        "The attention to detail and commitment to quality is outstanding. I'm happy with the service I have received so far.",
                      image: "/zseLogo.png"
                    },
                  ].map((testimonial) => (
                    <div key={testimonial.name}>
                      <Card className="border-0 shadow-lg hover:shadow-2xl transition-all duration-500 backdrop-blur-xl bg-white/90 dark:bg-gray-900/90 border-gray-200/30 dark:border-gray-700/30 overflow-hidden relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-[#4CAA7D]/10 to-[#4CAA7D]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                        <CardContent className="p-8 relative z-10">
                          <div className="flex mb-4">
                            {[...Array(5)].map((_, i) => (
                              <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            ))}
                          </div>
                          <p className="text-gray-600 dark:text-gray-300 mb-6">"{testimonial.content}"</p>
                          <div className="flex items-center">
                            <Image
                              src={testimonial.image}
                              alt={`${testimonial.name} - ${testimonial.role}`}
                              width={40}
                              height={40}
                              className="rounded-full mr-3 object-cover"
                            />
                            <div>
                              <div className="font-semibold text-gray-900 dark:text-white">{testimonial.name}</div>
                              <div className="text-sm text-gray-600 dark:text-gray-300">{testimonial.role}</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Pricing Section */}
            <section
              id="pricing"
              className="bg-gradient-to-br from-gray-50/80 via-white/50 to-gray-100/80 dark:from-gray-900/80 dark:via-gray-950/50 dark:to-gray-900/80 transition-colors backdrop-blur-sm"
            >
              <PricingSection />
            </section>

            {/* Contact Form */}
            <section id="contact" className="dark:bg-gray-950/80 transition-colors backdrop-blur-sm">
              <ContactForm />
            </section>

            
          </main>

          {/* Footer */}
          <footer className="border-t bg-gradient-to-br from-gray-50/90 via-white/70 to-gray-100/90 dark:from-gray-900/90 dark:via-gray-950/70 dark:to-gray-900/90 dark:border-gray-800 transition-colors backdrop-blur-xl">
            <div className="px-4 md:px-6 py-12">
              <div className="grid gap-8 md:grid-cols-4">
                <FadeInUp>
                  <div>
                    <motion.div className="flex items-center space-x-2 mb-4" whileHover={{ scale: 1.05 }}>
                      <motion.div
                        className="flex h-8 w-8 items-center justify-center rounded-lg bg-transparent"
                        animate={{
                          boxShadow: [
                            "0 0 20px rgba(0,0,0,0.3)",
                            "0 0 30px rgba(76,170,125,0.5)",
                            "0 0 20px rgba(0,0,0,0.3)",
                          ],
                        }}
                        transition={{
                          duration: 3,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: "easeInOut",
                        }}
                      >
                        <Image
                          src="/musasa_darker_green.png"
                          alt="Musasa Logo"
                          width={32}
                          height={32}
                          className="h-8 w-8 object-contain dark:hidden"
                        />
                        <Image
                          src="/musasa_white.png"
                          alt="Musasa Logo"
                          width={32}
                          height={32}
                          className="h-8 w-8 object-contain hidden dark:block"
                        />
                      </motion.div>
                      <span className="text-xl font-bold text-black dark:text-white">MUSASA</span>
                    </motion.div>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Crafting digital experiences that drive results for modern businesses.
                    </p>
                  </div>
                </FadeInUp>

                <FadeInUp delay={0.1}>
                  <div>
                    <h3 className="font-semibold mb-4 dark:text-white">Services</h3>
                    <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                      <li>
                        <Link href="#" className="hover:text-[#4CAA7D] dark:hover:text-[#4CAA7D] transition-colors">
                          Web Development
                        </Link>
                      </li>
                      <li>
                        <Link href="#" className="hover:text-[#4CAA7D] dark:hover:text-[#4CAA7D] transition-colors">
                          UI/UX Design
                        </Link>
                      </li>
                      <li>
                        <Link href="#" className="hover:text-[#4CAA7D] dark:hover:text-[#4CAA7D] transition-colors">
                          Mobile Apps
                        </Link>
                      </li>
                      <li>
                        <Link href="#" className="hover:text-[#4CAA7D] dark:hover:text-[#4CAA7D] transition-colors">
                          Consulting
                        </Link>
                      </li>
                    </ul>
                  </div>
                </FadeInUp>

                <FadeInUp delay={0.2}>
                  <div>
                    <h3 className="font-semibold mb-4 dark:text-white">Company</h3>
                    <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                      <li>
                        <Link href="#" className="hover:text-[#4CAA7D] dark:hover:text-[#4CAA7D] transition-colors">
                          About
                        </Link>
                      </li>
                      <li>
                        <Link href="#" className="hover:text-[#4CAA7D] dark:hover:text-[#4CAA7D] transition-colors">
                          Portfolio
                        </Link>
                      </li>
                      <li>
                        <Link href="#" className="hover:text-[#4CAA7D] dark:hover:text-[#4CAA7D] transition-colors">
                          Blog
                        </Link>
                      </li>
                      <li>
                        <Link href="#" className="hover:text-[#4CAA7D] dark:hover:text-[#4CAA7D] transition-colors">
                          Careers
                        </Link>
                      </li>
                    </ul>
                  </div>
                </FadeInUp>

                <FadeInUp delay={0.3}>
                  <div>
                    <h3 className="font-semibold mb-4 dark:text-white">Contact</h3>
                    <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                      <li><EMAIL></li>
                      <li>+1 (555) 123-4567</li>
                      <li>San Francisco, CA</li>
                    </ul>
                  </div>
                </FadeInUp>
              </div>

              <div className="border-t dark:border-gray-800 mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  © {new Date().getFullYear()} Musasa. All rights reserved.
                </p>
                <div className="flex space-x-6 mt-4 sm:mt-0">
                  <Link
                    href="#"
                    className="text-sm text-gray-600 dark:text-gray-300 hover:text-[#4CAA7D] dark:hover:text-[#4CAA7D] transition-colors"
                  >
                    Privacy Policy
                  </Link>
                  <Link
                    href="#"
                    className="text-sm text-gray-600 dark:text-gray-300 hover:text-[#4CAA7D] dark:hover:text-[#4CAA7D] transition-colors"
                  >
                    Terms of Service
                  </Link>
                </div>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </>
  )
}
