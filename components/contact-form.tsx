"use client"

import type React from "react"

import { motion, useMotionValue, useTransform } from "framer-motion"
import { useState } from "react"
import { Send, User, Mail, MessageSquare, Building } from "lucide-react"
import { FadeInUp, SlideInLeft, SlideInRight } from "./animated-elements"

export function ContactForm() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    message: "",
  })

  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)

  const rotateX = useTransform(mouseY, [-300, 300], [5, -5])
  const rotateY = useTransform(mouseX, [-300, 300], [-5, 5])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log("Form submitted:", formData)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    mouseX.set(e.clientX - centerX)
    mouseY.set(e.clientY - centerY)
  }

  return (
    <section className="py-20 relative overflow-hidden">
      <div className="px-4 md:px-6 relative z-10">
        <FadeInUp>
          <div className="text-center mb-16">
            <motion.h2
              className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 drop-shadow-lg"
              animate={{
                textShadow: [
                  "0 0 20px rgba(76,170,125,0.3)",
                  "0 0 30px rgba(76,170,125,0.4)",
                  "0 0 20px rgba(76,170,125,0.3)",
                ],
              }}
              transition={{
                duration: 4,
                repeat: Number.POSITIVE_INFINITY,
                ease: "easeInOut",
              }}
            >
              Let's Create Something Amazing
            </motion.h2>
            <p className="text-xl text-gray-700 dark:text-gray-300 max-w-2xl mx-auto drop-shadow-md">
              Ready to transform your ideas into reality? Get in touch and let's discuss your project.
            </p>
          </div>
        </FadeInUp>

        <div className="grid gap-12 lg:grid-cols-2 max-w-6xl mx-auto items-start">
          <SlideInLeft>
            <div className="space-y-8">
              <motion.div
                className="space-y-8"
                animate={{
                  y: [0, -10, 0],
                }}
                transition={{
                  duration: 6,
                  repeat: Number.POSITIVE_INFINITY,
                  ease: "easeInOut",
                }}
              >
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 drop-shadow-lg">Why Choose Musasa?</h3>

                {[
                  {
                    icon: "🚀",
                    title: "Lightning Fast Delivery",
                    desc: "We deliver projects 40% faster than industry average",
                  },
                  {
                    icon: "💎",
                    title: "Premium Quality",
                    desc: "Award-winning designs that convert visitors to customers",
                  },
                  { 
                    icon: "🔧", 
                    title: "Ongoing Support", 
                    desc: "24/7 support and maintenance for peace of mind" 
                  },
                  { 
                    icon: "📈", 
                    title: "Growth Focused", 
                    desc: "Built to scale with your business growth" 
                  },
                ].map((item, index) => (
                  <motion.div
                    key={item.title}
                    className="flex items-start space-x-4 group"
                    initial={{ opacity: 0, x: -50 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ x: 10 }}
                  >
                    <motion.div
                      className="text-3xl flex-shrink-0"
                      animate={{
                        rotate: [0, 10, -10, 0],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Number.POSITIVE_INFINITY,
                        ease: "easeInOut",
                        delay: index * 0.5,
                      }}
                    >
                      {item.icon}
                    </motion.div>
                    <div>
                      <h4 className="font-bold text-gray-900 dark:text-white text-lg mb-2 group-hover:text-[#4CAA7D] drop-shadow-md transition-colors">
                        {item.title}
                      </h4>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{item.desc}</p>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </div>
          </SlideInLeft>

          <SlideInRight>
            <motion.div
              onMouseMove={handleMouseMove}
              style={{
                rotateX,
                rotateY,
                transformStyle: "preserve-3d",
              }}
              className="w-full"
            >
              {/* Dark Mode Responsive Contact Form Card */}
              <motion.div
                className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl p-8 border border-gray-200 dark:border-gray-700"
                whileHover={{
                  y: -5,
                  boxShadow: "0 25px 50px rgba(0,0,0,0.15)",
                }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 30,
                }}
              >
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-2">
                    <motion.div className="relative" whileFocus={{ scale: 1.02 }}>
                      <User className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-5 h-5" />
                      <input
                        type="text"
                        name="name"
                        placeholder="Your Name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full pl-12 pr-4 py-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#4CAA7D] dark:focus:ring-[#4CAA7D] focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200"
                      />
                    </motion.div>

                    <motion.div className="relative" whileFocus={{ scale: 1.02 }}>
                      <Building className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-5 h-5" />
                      <input
                        type="text"
                        name="company"
                        placeholder="Company"
                        value={formData.company}
                        onChange={handleInputChange}
                        className="w-full pl-12 pr-4 py-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#4CAA7D] dark:focus:ring-[#4CAA7D] focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200"
                      />
                    </motion.div>
                  </div>

                  <motion.div className="relative" whileFocus={{ scale: 1.02 }}>
                    <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-5 h-5" />
                    <input
                      type="email"
                      name="email"
                      placeholder="Email Address"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full pl-12 pr-4 py-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#4CAA7D] dark:focus:ring-[#4CAA7D] focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200"
                    />
                  </motion.div>

                  <motion.div className="relative" whileFocus={{ scale: 1.02 }}>
                    <MessageSquare className="absolute left-4 top-4 text-gray-400 dark:text-gray-500 w-5 h-5" />
                    <textarea
                      name="message"
                      rows={5}
                      placeholder="Tell us about your project..."
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      className="w-full pl-12 pr-4 py-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#4CAA7D] dark:focus:ring-[#4CAA7D] focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none transition-all duration-200"
                    />
                  </motion.div>

                  <motion.button
                    type="submit"
                    className="w-full py-4 px-6 bg-gradient-to-r from-[#4CAA7D] to-[#4CAA7D] hover:from-[#4CAA7D] hover:to-[#4CAA7D] dark:from-[#4CAA7D] dark:to-[#4CAA7D] dark:hover:from-[#4CAA7D] dark:hover:to-[#4CAA7D] text-white font-semibold rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Send className="w-5 h-5" />
                    <span>Send Message</span>
                  </motion.button>
                </form>
              </motion.div>
            </motion.div>
          </SlideInRight>
        </div>
      </div>
    </section>
  )
}
