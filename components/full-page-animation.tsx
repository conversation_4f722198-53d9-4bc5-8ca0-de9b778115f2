"use client"

import { motion } from "framer-motion"
import { useEffect, useState } from "react"

export function FullPageAnimation() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener("mousemove", updateMousePosition)
    return () => window.removeEventListener("mousemove", updateMousePosition)
  }, [])

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {/* Animated Grid */}
      <div className="absolute inset-0 opacity-10 dark:opacity-5">
        <svg width="100%" height="100%" className="absolute inset-0">
          <defs>
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="currentColor" strokeWidth="1" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      {/* Large Floating Orbs */}
      <motion.div
        className="absolute w-96 h-96 rounded-full bg-gradient-to-r from-[#4CAA7D] to-[#4CAA7D] opacity-10 dark:opacity-5 blur-3xl"
        animate={{
          x: [0, 200, 0],
          y: [0, -100, 0],
        }}
        transition={{
          duration: 20,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
        style={{
          left: "5%",
          top: "10%",
        }}
      />

      <motion.div
        className="absolute w-80 h-80 rounded-full bg-gradient-to-r from-blue-400 to-cyan-400 opacity-10 dark:opacity-5 blur-3xl"
        animate={{
          x: [0, -150, 0],
          y: [0, 120, 0],
        }}
        transition={{
          duration: 25,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
          delay: 2,
        }}
        style={{
          right: "10%",
          top: "20%",
        }}
      />

      <motion.div
        className="absolute w-64 h-64 rounded-full bg-gradient-to-r from-[#4CAA7D] to-blue-400 opacity-10 dark:opacity-5 blur-3xl"
        animate={{
          x: [0, 100, 0],
          y: [0, -80, 0],
        }}
        transition={{
          duration: 18,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
          delay: 5,
        }}
        style={{
          left: "20%",
          top: "60%",
        }}
      />

      <motion.div
        className="absolute w-72 h-72 rounded-full bg-gradient-to-r from-[#4CAA7D] to-[#4CAA7D] opacity-8 dark:opacity-4 blur-3xl"
        animate={{
          x: [0, -120, 0],
          y: [0, 90, 0],
        }}
        transition={{
          duration: 22,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
          delay: 8,
        }}
        style={{
          right: "15%",
          top: "70%",
        }}
      />

      {/* Interactive Cursor Effect */}
      <motion.div
        className="absolute w-8 h-8 rounded-full bg-black dark:bg-white opacity-5 dark:opacity-3 blur-sm"
        animate={{
          x: mousePosition.x - 16,
          y: mousePosition.y - 16,
        }}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 28,
        }}
      />

      {/* Animated Code Blocks - Multiple positioned throughout */}
      <motion.div
        className="absolute left-10 opacity-8 dark:opacity-4 hidden lg:block"
        animate={{
          rotate: [0, 5, 0],
          y: [0, -20, 0],
        }}
        transition={{
          duration: 8,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
        style={{ top: "15%" }}
      >
        <div className="bg-gray-900 dark:bg-gray-900 p-3 rounded-lg text-xs font-mono text-white">
          <div>{'<div className="hero">'}</div>
          <div className="ml-2">{"<h1>Hello World</h1>"}</div>
          <div>{"</div>"}</div>
        </div>
      </motion.div>

      <motion.div
        className="absolute right-10 opacity-8 dark:opacity-4 hidden lg:block"
        animate={{
          rotate: [0, -5, 0],
          y: [0, 15, 0],
        }}
        transition={{
          duration: 10,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
          delay: 3,
        }}
        style={{ top: "35%" }}
      >
        <div className="bg-gray-900 dark:bg-gray-900 p-3 rounded-lg text-xs font-mono text-white">
          <div>{"const magic = () => {"}</div>
          <div className="ml-2">{'return "✨"'}</div>
          <div>{"}"}</div>
        </div>
      </motion.div>

      <motion.div
        className="absolute left-20 opacity-8 dark:opacity-4 hidden lg:block"
        animate={{
          rotate: [0, 3, 0],
          y: [0, -10, 0],
        }}
        transition={{
          duration: 12,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
          delay: 6,
        }}
        style={{ top: "65%" }}
      >
        <div className="bg-gray-900 dark:bg-gray-900 p-3 rounded-lg text-xs font-mono text-white">
          <div>{"function build() {"}</div>
          <div className="ml-2">{'return "🚀"'}</div>
          <div>{"}"}</div>
        </div>
      </motion.div>

      <motion.div
        className="absolute right-16 opacity-8 dark:opacity-4 hidden lg:block"
        animate={{
          rotate: [0, -3, 0],
          y: [0, 12, 0],
        }}
        transition={{
          duration: 9,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
          delay: 9,
        }}
        style={{ top: "80%" }}
      >
        <div className="bg-gray-900 dark:bg-gray-900 p-3 rounded-lg text-xs font-mono text-white">
          <div>{"// Innovation"}</div>
          <div>{"deploy()"}</div>
        </div>
      </motion.div>

      {/* Enhanced Particle System - More particles across the page */}
      {[...Array(40)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-1 h-1 bg-gray-400 dark:bg-gray-600 rounded-full opacity-20 dark:opacity-10"
          animate={{
            y: [0, -200, 0],
            opacity: [0, 0.3, 0],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 4 + Math.random() * 3,
            repeat: Number.POSITIVE_INFINITY,
            delay: Math.random() * 5,
            ease: "easeOut",
          }}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${60 + Math.random() * 40}%`,
          }}
        />
      ))}

      {/* Floating Geometric Shapes */}
      <motion.div
        className="absolute w-4 h-4 bg-gray-300 dark:bg-gray-700 opacity-20 dark:opacity-10 blur-sm"
        animate={{
          rotate: [0, 360],
          y: [0, -30, 0],
        }}
        transition={{
          duration: 15,
          repeat: Number.POSITIVE_INFINITY,
          ease: "linear",
        }}
        style={{
          left: "15%",
          top: "25%",
        }}
      />

      <motion.div
        className="absolute w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 opacity-20 dark:opacity-10 blur-sm"
        animate={{
          y: [0, -40, 0],
          x: [0, 20, 0],
        }}
        transition={{
          duration: 12,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
          delay: 2,
        }}
        style={{
          right: "25%",
          top: "45%",
        }}
      />

      <motion.div
        className="absolute w-5 h-5 bg-gray-300 dark:bg-gray-700 opacity-20 dark:opacity-10 transform rotate-45 blur-sm"
        animate={{
          rotate: [45, 405],
          y: [0, -25, 0],
        }}
        transition={{
          duration: 18,
          repeat: Number.POSITIVE_INFINITY,
          ease: "linear",
          delay: 4,
        }}
        style={{
          left: "70%",
          top: "55%",
        }}
      />

      {/* Subtle Wave Effect */}
      <motion.div
        className="absolute inset-0 opacity-5 dark:opacity-2"
        animate={{
          backgroundPosition: ["0% 0%", "100% 100%"],
        }}
        transition={{
          duration: 30,
          repeat: Number.POSITIVE_INFINITY,
          ease: "linear",
        }}
        style={{
          background: "radial-gradient(circle at 50% 50%, rgba(0,0,0,0.1) 0%, transparent 50%)",
          backgroundSize: "200% 200%",
        }}
      />
    </div>
  )
}
