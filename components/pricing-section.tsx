"use client"

import { motion } from "framer-motion"
import { Check } from "lucide-react"
import { FadeInUp, ScaleIn } from "./animated-elements"

export function PricingSection() {
  const plans = [
    {
      name: "Essentials",
      price: "$230",
      period: "once-off",
      description: "Perfect for small businesses and startups.",
      features: [
        "1-5 pages",
        "3-day delivery", 
        "Mobile responsive website",
        "Basic SEO",
        "Contact form"
      ],
      popular: false,
      buttonText: "Get Started",
      buttonStyle: "bg-gray-600 hover:bg-gray-700 text-white"
    },
    {
      name: "Premium",
      price: "$600", 
      period: "once-off",
      description: "Ideal for growing businesses.",
      features: [
        "Up to 10 pages",
        "5-day delivery",
        "Mobile Responsive Website", 
        "Advanced SEO",
        "Free Support for the 1st month",
        "Content Management Tools"
      ],
      popular: true,
      buttonText: "Get Started",
      buttonStyle: "bg-[#4CAA7D] hover:bg-[#4CAA7D] text-white"
    },
    {
      name: "Tailored Solutions",
      price: "-",
      period: "once-off", 
      description: "For large organizations and uncommon needs.",
      features: [
        "Web Applications",
        "Mobile Applications",
        "Group Websites",
        "AI Integration & Automation",
        "Consulting",
      ],
      popular: false,
      buttonText: "Contact Sales",
      buttonStyle: "bg-gray-600 hover:bg-gray-700 text-white"
    }
  ]

  return (
    <section id="pricing" className="py-20 bg-gradient-to-br from-gray-50/80 via-white/50 to-gray-100/80 dark:from-gray-900/80 dark:via-gray-950/50 dark:to-gray-900/80 transition-colors backdrop-blur-sm">
      <div className="px-4 md:px-6">
        <div className="mx-auto max-w-2xl text-center mb-16">
          <motion.h2
            className="text-3xl font-bold tracking-tight sm:text-4xl mb-4 dark:text-white"
            animate={{
              backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
            }}
            transition={{
              duration: 4,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
            }}
            style={{
              background: "linear-gradient(45deg, #1f2937, #4CAA7D, #4CAA7D, #1f2937)",
              backgroundSize: "200% 200%",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
            }}
          >
            Simple, transparent pricing
          </motion.h2>
          <p className="text-gray-600 dark:text-gray-300">
            Choose the perfect plan for your business needs
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-3 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              className={`relative p-8 rounded-2xl border transition-all duration-300 ${
                plan.popular 
                  ? "border-[#4CAA7D] bg-white dark:bg-gray-900" 
                  : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 hover:border-gray-300 dark:hover:border-gray-600"
              }`}
              whileHover={{ y: -5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className="bg-[#4CAA7D] text-white px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </div>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{plan.name}</h3>
                <div className="mb-4">
                  <span className="text-5xl font-bold text-gray-900 dark:text-white">{plan.price}</span>
                  <span className="text-gray-600 dark:text-gray-400 ml-1">/{plan.period}</span>
                </div>
                <p className="text-gray-600 dark:text-gray-400">{plan.description}</p>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <motion.li
                    key={feature}
                    className="flex items-center"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: featureIndex * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className="flex-shrink-0 w-5 h-5 bg-[#4CAA7D] rounded-full flex items-center justify-center mr-3">
                      <Check className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                  </motion.li>
                ))}
              </ul>

              <button 
                className={`w-full py-3 px-6 rounded-lg font-semibold transition-colors ${plan.buttonStyle}`}
                onClick={() => window.open('https://cal.com/shaunnn/discovery-session', '_blank')}
              >
                {plan.buttonText}
              </button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
