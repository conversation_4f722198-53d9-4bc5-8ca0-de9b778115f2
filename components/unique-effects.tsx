"use client"

import { motion, useMotionValue, useSpring } from "framer-motion"
import { useEffect, useState } from "react"

export function MorphingBlob() {
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)

  const springX = useSpring(mouseX, { stiffness: 100, damping: 30 })
  const springY = useSpring(mouseY, { stiffness: 100, damping: 30 })

  useEffect(() => {
    const updateMousePosition = (e: MouseEvent) => {
      mouseX.set(e.clientX)
      mouseY.set(e.clientY)
    }

    window.addEventListener("mousemove", updateMousePosition)
    return () => window.removeEventListener("mousemove", updateMousePosition)
  }, [mouseX, mouseY])

  return (
    <motion.div
      className="fixed pointer-events-none z-0"
      style={{
        x: springX,
        y: springY,
        translateX: "-50%",
        translateY: "-50%",
      }}
    >
      <motion.div
        className="w-32 h-32 opacity-5 dark:opacity-3"
        animate={{
          borderRadius: [
            "60% 40% 30% 70%/60% 30% 70% 40%",
            "30% 60% 70% 40%/50% 60% 30% 60%",
            "60% 40% 30% 70%/60% 30% 70% 40%",
          ],
        }}
        transition={{
          duration: 8,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
        style={{
          background: "linear-gradient(45deg, #e5e7eb, #f3f4f6, #d1d5db, #e5e7eb)",
          filter: "blur(20px)",
        }}
      />
    </motion.div>
  )
}

export function InteractiveParticles() {
  const [particles, setParticles] = useState<Array<{ id: number; x: number; y: number }>>([])

  useEffect(() => {
    const newParticles = Array.from({ length: 50 }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
    }))
    setParticles(newParticles)
  }, [])

  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      <svg width="100%" height="100%" className="absolute inset-0">
        {particles.map((particle, index) => (
          <motion.circle
            key={particle.id}
            cx={particle.x}
            cy={particle.y}
            r="2"
            fill="currentColor"
            className="text-gray-400 dark:text-gray-600 opacity-30"
            animate={{
              cx: [particle.x, particle.x + Math.random() * 100 - 50, particle.x],
              cy: [particle.y, particle.y + Math.random() * 100 - 50, particle.y],
            }}
            transition={{
              duration: 10 + Math.random() * 10,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
              delay: index * 0.1,
            }}
          />
        ))}

        {/* Connection lines between nearby particles */}
        {particles.map((particle, i) =>
          particles.slice(i + 1).map((otherParticle, j) => {
            const distance = Math.sqrt(
              Math.pow(particle.x - otherParticle.x, 2) + Math.pow(particle.y - otherParticle.y, 2),
            )
            if (distance < 150) {
              return (
                <motion.line
                  key={`${i}-${j}`}
                  x1={particle.x}
                  y1={particle.y}
                  x2={otherParticle.x}
                  y2={otherParticle.y}
                  stroke="currentColor"
                  strokeWidth="1"
                  className="text-gray-300 dark:text-gray-700"
                  opacity={0.1}
                  animate={{
                    opacity: [0.1, 0.3, 0.1],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Number.POSITIVE_INFINITY,
                    ease: "easeInOut",
                  }}
                />
              )
            }
            return null
          }),
        )}
      </svg>
    </div>
  )
}

export function LiquidButton({ children, ...props }: any) {
  return (
    <motion.button
      className="relative overflow-hidden px-8 py-4 rounded-full bg-gradient-to-r from-gray-800 to-gray-900 dark:from-gray-100 dark:to-gray-200 text-white dark:text-black font-semibold"
      whileHover="hover"
      whileTap="tap"
      variants={{
        hover: { scale: 1.05 },
        tap: { scale: 0.95 },
      }}
      {...props}
    >
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-gray-900 to-gray-800 dark:from-gray-200 dark:to-gray-100"
        variants={{
          hover: {
            scale: 1.1,
          },
        }}
        transition={{
          duration: 0.3,
          ease: "easeInOut",
        }}
      />
      <span className="relative z-10">{children}</span>
    </motion.button>
  )
}

export function GlassmorphicCard({ children, className = "", ...props }: any) {
  return (
    <motion.div
      className={`backdrop-blur-xl bg-gray-900/90 dark:bg-gray-900/90 border border-gray-700/30 rounded-3xl shadow-2xl ${className}`}
      whileHover={{
        y: -10,
        rotateX: 5,
        rotateY: 5,
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
      }}
      style={{
        transformStyle: "preserve-3d",
      }}
      {...props}
    >
      {children}
    </motion.div>
  )
}
